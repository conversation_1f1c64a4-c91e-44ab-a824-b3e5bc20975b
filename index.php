<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$role = $_SESSION['role'] ?? 'employee';
$employee_id = $_SESSION['employee_id'] ?? 0;

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("DB connection failed.");
}

// Fetch employees for admin, or single employee record for normal employee
if ($role === 'admin') {
  $result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
  $employees = [];
  while ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
} else {
  $result = $mysqli->query("SELECT id, name FROM employees WHERE id = " . intval($employee_id));
  $employees = [];
  if ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Clock In/Out - Attendance App</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<style>
  #video, #canvas { width: 100%; border-radius: 8px; margin-top: 10px; }
  #map { height: 200px; margin-top: 10px; }
  body { max-width: 600px; margin: 20px auto; }
</style>
<link rel="stylesheet" href="style.css" />
</head>
<body>
<div class="container">
<h1>Clock In/Out</h1>
<form id="clockForm" method="POST" action="submit_attendance.php" enctype="multipart/form-data" novalidate>
  <label for="employee" class="form-label">Select Employee:</label>
  <select name="employee_id" id="employee" class="form-select" required <?=($role !== 'admin') ? 'disabled' : ''?>>
    <option value="">Select employee</option>
    <?php foreach($employees as $emp): ?>
      <option value="<?= $emp['id'] ?>" <?= ($emp['id'] == $employee_id) ? 'selected' : '' ?>>
        <?= htmlspecialchars($emp['name']) ?>
      </option>
    <?php endforeach; ?>
  </select>

  <?php if ($role !== 'admin'): ?>
    <input type="hidden" name="employee_id" value="<?= intval($employee_id) ?>" />
  <?php endif; ?>

  <label>Action:</label><br/>
  <button type="button" id="clockInBtn" class="btn btn-success me-2">Clock In</button>
  <button type="button" id="clockOutBtn" class="btn btn-danger">Clock Out</button>
  <input type="hidden" name="type" id="type" />

  <label>Your Location (will be auto-collected):</label>
  <input type="text" id="locationDisplay" disabled class="form-control" placeholder="Waiting for location..." />

  <input type="hidden" name="lat" id="lat" />
  <input type="hidden" name="lng" id="lng" />

  <label>Take a Selfie (click video to capture):</label>
  <video id="video" autoplay playsinline class="border border-secondary rounded"></video>
  <canvas id="canvas" style="display:none;"></canvas>
  <input type="hidden" name="selfie" id="selfie" />

  <div id="map"></div>

  <button type="submit" id="submitBtn" class="btn btn-primary mt-3" disabled>Submit Clock In/Out</button>
</form>

<a href="dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
</div>

<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
// The full JS from the previous answer goes here exactly, omitted for brevity (contact me if you want the full script again)
</script>
</body>
</html>
