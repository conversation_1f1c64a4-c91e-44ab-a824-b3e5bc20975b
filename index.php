<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$role = $_SESSION['role'] ?? 'employee';
$employee_id = $_SESSION['employee_id'] ?? 0;

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("DB connection failed.");
}

// Fetch employees for admin, or single employee record for normal employee
if ($role === 'admin') {
  $result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
  $employees = [];
  while ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
} else {
  $result = $mysqli->query("SELECT id, name FROM employees WHERE id = " . intval($employee_id));
  $employees = [];
  if ($row = $result->fetch_assoc()) {
    $employees[] = $row;
  }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Clock In/Out - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Clock In/Out</h1>
          <p class="page-subtitle">Record your attendance with location and photo verification</p>
        </div>
        <div>
          <a href="dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Clock Form -->
    <div class="content-card">
      <form id="clockForm" method="POST" action="submit_attendance.php" enctype="multipart/form-data" novalidate>

        <!-- Employee Selection -->
        <div class="form-group">
          <label for="employee" class="form-label">
            <i class="bi bi-person me-2"></i>
            Select Employee
          </label>
          <select name="employee_id" id="employee" class="form-select" required <?=($role !== 'admin') ? 'disabled' : ''?>>
            <option value="">Choose an employee</option>
            <?php foreach($employees as $emp): ?>
              <option value="<?= $emp['id'] ?>" <?= ($emp['id'] == $employee_id) ? 'selected' : '' ?>>
                <?= htmlspecialchars($emp['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
          <?php if ($role !== 'admin'): ?>
            <input type="hidden" name="employee_id" value="<?= intval($employee_id) ?>" />
          <?php endif; ?>
        </div>

        <!-- Action Buttons -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-clock me-2"></i>
            Choose Action
          </label>
          <div class="d-grid gap-2 d-md-flex">
            <button type="button" id="clockInBtn" class="btn btn-success flex-fill">
              <i class="bi bi-play-circle me-2"></i>
              Clock In
            </button>
            <button type="button" id="clockOutBtn" class="btn btn-danger flex-fill">
              <i class="bi bi-stop-circle me-2"></i>
              Clock Out
            </button>
          </div>
          <input type="hidden" name="type" id="type" />
        </div>

        <!-- Location -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-geo-alt me-2"></i>
            Your Location
          </label>
          <input type="text" id="locationDisplay" disabled class="form-control" placeholder="Detecting location..." />
          <input type="hidden" name="lat" id="lat" />
          <input type="hidden" name="lng" id="lng" />
        </div>

        <!-- Camera Section -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-camera me-2"></i>
            Take a Selfie
          </label>
          <div class="camera-container">
            <video id="video" autoplay playsinline class="camera-video"></video>
            <canvas id="canvas" style="display:none;"></canvas>
            <div class="camera-overlay">
              <button type="button" class="btn btn-light btn-sm" onclick="capturePhoto()">
                <i class="bi bi-camera"></i>
                Capture
              </button>
            </div>
          </div>
          <input type="hidden" name="selfie" id="selfie" />
        </div>

        <!-- Map -->
        <div class="form-group">
          <label class="form-label">
            <i class="bi bi-map me-2"></i>
            Location Map
          </label>
          <div id="map" class="map-container"></div>
        </div>

        <!-- Submit Button -->
        <button type="submit" id="submitBtn" class="btn btn-primary w-100" disabled>
          <i class="bi bi-check-circle me-2"></i>
          Submit Attendance
        </button>
      </form>
    </div>
  </div>

  <style>
    .camera-container {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      background: #f8f9fa;
      border: 2px solid var(--border-color);
    }

    .camera-video {
      width: 100%;
      height: 300px;
      object-fit: cover;
      border-radius: 10px;
    }

    .camera-overlay {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .map-container {
      height: 250px;
      border-radius: 12px;
      overflow: hidden;
      border: 2px solid var(--border-color);
    }

    #submitBtn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .form-group {
      margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
      .camera-video {
        height: 250px;
      }

      .map-container {
        height: 200px;
      }
    }
  </style>

<script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
<script>
// The full JS from the previous answer goes here exactly, omitted for brevity (contact me if you want the full script again)
</script>
</body>
</html>
