<?php
session_start();
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';

$host = "localhost";
$user = "root";
$pass = "";
$db = "attendance_db2";

$mysqli = new mysqli($host, $user, $pass, $db);
if ($mysqli->connect_errno) {
    http_response_code(500);
    die("Database connection failed.");
}

$role = $_SESSION['role'];
$employee_id_session = $_SESSION['employee_id'] ?? 0;

$employee_id = isset($_POST['employee_id']) ? intval($_POST['employee_id']) : 0;
$type = $_POST['type'] ?? null;
$lat = isset($_POST['lat']) ? floatval($_POST['lat']) : null;
$lng = isset($_POST['lng']) ? floatval($_POST['lng']) : null;
$selfieDataUrl = $_POST['selfie'] ?? null;

if ($role !== 'admin') { // employees can only clock for themselves
    $employee_id = $employee_id_session;
}

if (!$employee_id || !$type || !in_array($type, ['in', 'out']) || !$selfieDataUrl) {
    die("Missing or invalid data. <a href='index.php'>Back</a>");
}

// Validate employee exists
$stmt = $mysqli->prepare("SELECT id FROM employees WHERE id = ?");
$stmt->bind_param('i', $employee_id);
$stmt->execute();
$stmt->store_result();
if ($stmt->num_rows === 0) {
    die("Invalid employee. <a href='index.php'>Back</a>");
}
$stmt->close();

// Check shifts assigned today for employee (using date range)
$stmt = $mysqli->prepare("
    SELECT st.id, st.name, st.start_time, st.end_time
    FROM employee_shifts es
    JOIN shift_templates st ON es.shift_template_id = st.id
    WHERE es.employee_id = ? AND CURDATE() BETWEEN es.shift_start_date AND es.shift_end_date
");
$stmt->bind_param('i', $employee_id);
$stmt->execute();
$stmt->bind_result($st_id, $st_name, $st_start, $st_end);
$todayShifts = [];
while ($stmt->fetch()) {
    $todayShifts[] = ['id' => $st_id, 'name' => $st_name, 'start' => $st_start, 'end' => $st_end];
}
$stmt->close();

if (empty($todayShifts)) {
    die("You do not have any assigned shifts today, cannot clock in or out. <a href='index.php'>Back</a>");
}

// Validate current time is within any assigned shift time ± grace period
$nowMins = ((int)date('H') * 60) + (int)date('i');
$isValidShiftTime = false;
foreach ($todayShifts as $shift) {
    $startMins = timeToMinutes(substr($shift['start'], 0, 5));
    $endMins = timeToMinutes(substr($shift['end'], 0, 5));
    if ($nowMins >= $startMins - GRACE_PERIOD_MINUTES && $nowMins <= $endMins + 60) {
        $isValidShiftTime = true;
        break;
    }
}

if (!$isValidShiftTime) {
    die("Your current time is outside your assigned shift times. You cannot clock in or out now. <a href='index.php'>Back</a>");
}

// Save selfie image
if (preg_match('/^data:image\/(\w+);base64,/', $selfieDataUrl, $typeMatches)) {
    $imageType = strtolower($typeMatches[1]);
    if (!in_array($imageType, ['jpeg', 'jpg', 'png', 'gif'])) {
        die("Unsupported image type.");
    }
    $imageData = substr($selfieDataUrl, strpos($selfieDataUrl, ',') + 1);
    $imageData = base64_decode($imageData);

    if ($imageData === false) {
        die("Invalid image data.");
    }

    $uploadDir = __DIR__ . '/selfies/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    $filename = uniqid("selfie_") . '.' . $imageType;
    $filePath = $uploadDir . $filename;

    if (file_put_contents($filePath, $imageData) === false) {
        die("Failed to save image.");
    }

    $selfiePathForDb = 'selfies/' . $filename;
} else {
    die("Invalid selfie format.");
}

// Insert attendance record
$stmt = $mysqli->prepare("INSERT INTO attendance (employee_id, type, timestamp, lat, lng, selfie_path) VALUES (?, ?, NOW(), ?, ?, ?)");
$stmt->bind_param('issds', $employee_id, $type, $lat, $lng, $selfiePathForDb);
if ($stmt->execute()) {
    $stmt->close();
    header("Location: index.php?msg=Clocked $type successfully.");
    exit;
} else {
    die("Failed to save attendance record.");
}
