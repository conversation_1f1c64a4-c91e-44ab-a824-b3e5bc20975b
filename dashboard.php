<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$role = $_SESSION['role'];
$username = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Dashboard - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <style>
    :root {
      --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
      --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
      --dark-bg: #1a1d29;
      --card-bg: #ffffff;
      --text-primary: #2d3748;
      --text-secondary: #718096;
      --border-color: #e2e8f0;
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: var(--text-primary);
      line-height: 1.6;
    }

    .dashboard-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      min-height: 100vh;
    }

    .dashboard-header {
      background: var(--card-bg);
      border-radius: 20px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--border-color);
      position: relative;
      overflow: hidden;
    }

    .dashboard-header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--primary-gradient);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 1rem;
    }

    .welcome-section h1 {
      font-size: 2.5rem;
      font-weight: 700;
      background: var(--primary-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: 0.5rem;
    }

    .welcome-section p {
      color: var(--text-secondary);
      font-size: 1.1rem;
      font-weight: 400;
    }

    .header-actions {
      display: flex;
      gap: 1rem;
      align-items: center;
    }

    .user-avatar {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: var(--primary-gradient);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 1.2rem;
    }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: var(--card-bg);
      border-radius: 16px;
      padding: 1.5rem;
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }

    .stat-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
    }

    .stat-value {
      font-size: 2rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
    }

    .stat-label {
      color: var(--text-secondary);
      font-size: 0.9rem;
      font-weight: 500;
    }

    .main-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .feature-card {
      background: var(--card-bg);
      border-radius: 20px;
      padding: 2rem;
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--border-color);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      text-decoration: none;
      color: inherit;
    }

    .feature-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--primary-gradient);
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .feature-card:hover {
      transform: translateY(-8px);
      box-shadow: var(--shadow-xl);
      text-decoration: none;
      color: inherit;
    }

    .feature-card:hover::before {
      transform: scaleX(1);
    }

    .card-icon {
      width: 80px;
      height: 80px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      font-size: 2rem;
      color: white;
      position: relative;
      overflow: hidden;
    }

    .card-icon.employees {
      background: var(--primary-gradient);
    }

    .card-icon.shifts {
      background: var(--success-gradient);
    }

    .card-icon.reports {
      background: var(--warning-gradient);
    }

    .card-icon.clock {
      background: var(--info-gradient);
    }

    .card-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--text-primary);
    }

    .card-description {
      color: var(--text-secondary);
      font-size: 1rem;
      line-height: 1.5;
      margin-bottom: 1.5rem;
    }

    .card-footer {
      display: flex;
      justify-content: between;
      align-items: center;
      margin-top: auto;
    }

    .card-action {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      color: #667eea;
      font-weight: 500;
      font-size: 0.9rem;
      transition: all 0.2s ease;
    }

    .card-action:hover {
      gap: 0.75rem;
    }

    .logout-section {
      text-align: center;
      margin-top: 3rem;
    }

    .logout-btn {
      background: var(--danger-gradient);
      border: none;
      color: white;
      padding: 1rem 2rem;
      border-radius: 50px;
      font-weight: 600;
      font-size: 1.1rem;
      transition: all 0.3s ease;
      box-shadow: var(--shadow-md);
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }

    .logout-btn:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-xl);
      color: white;
      text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-container {
        padding: 1rem;
      }

      .dashboard-header {
        padding: 1.5rem;
        border-radius: 16px;
      }

      .welcome-section h1 {
        font-size: 2rem;
      }

      .header-content {
        flex-direction: column;
        text-align: center;
      }

      .main-cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .feature-card {
        padding: 1.5rem;
        border-radius: 16px;
      }

      .quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      }
    }

    @media (max-width: 480px) {
      .welcome-section h1 {
        font-size: 1.75rem;
      }

      .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
      }

      .card-title {
        font-size: 1.25rem;
      }
    }

    /* Loading Animation */
    .loading {
      opacity: 0;
      animation: fadeIn 0.6s ease forwards;
    }

    @keyframes fadeIn {
      to {
        opacity: 1;
      }
    }

    /* Pulse animation for stats */
    .stat-card .stat-value {
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 1;
      }
      50% {
        opacity: 0.8;
      }
    }
  </style>
</head>
<body>
  <div class="dashboard-container loading">
    <!-- Dashboard Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1>Welcome back, <?=htmlspecialchars($username)?>!</h1>
          <p><?= $role === 'admin' ? 'Admin Dashboard' : 'Employee Dashboard' ?> • <?= date('l, F j, Y') ?></p>
        </div>
        <div class="header-actions">
          <div class="user-avatar">
            <?= strtoupper(substr($username, 0, 1)) ?>
          </div>
        </div>
      </div>
    </div>

    <?php if ($role === 'admin'): ?>
      <!-- Quick Stats for Admin -->
      <div class="quick-stats">
        <div class="stat-card">
          <div class="stat-value" style="color: #667eea;">
            <?php
            $mysqli = new mysqli("localhost", "root", "", "attendance_db2");
            $result = $mysqli->query("SELECT COUNT(*) as count FROM employees");
            $count = $result->fetch_assoc()['count'];
            echo $count;
            ?>
          </div>
          <div class="stat-label">Total Employees</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" style="color: #4facfe;">
            <?php
            $result = $mysqli->query("SELECT COUNT(*) as count FROM shift_templates");
            $count = $result->fetch_assoc()['count'];
            echo $count;
            ?>
          </div>
          <div class="stat-label">Active Shifts</div>
        </div>
        <div class="stat-card">
          <div class="stat-value" style="color: #43e97b;">
            <?php
            $today = date('Y-m-d');
            $result = $mysqli->query("SELECT COUNT(*) as count FROM attendance WHERE DATE(clock_in) = '$today'");
            $count = $result->fetch_assoc()['count'];
            echo $count;
            ?>
          </div>
          <div class="stat-label">Today's Check-ins</div>
        </div>
      </div>
    <?php endif; ?>

    <!-- Main Feature Cards -->
    <div class="main-cards">
      <?php if ($role === 'admin'): ?>

        <a href="manage_employees.php" class="feature-card">
          <div class="card-icon employees">
            <i class="bi bi-people-fill"></i>
          </div>
          <div class="card-title">Manage Employees</div>
          <div class="card-description">Add, edit, or remove employees from your organization. Manage employee profiles and access permissions.</div>
          <div class="card-footer">
            <span class="card-action">
              Get started <i class="bi bi-arrow-right"></i>
            </span>
          </div>
        </a>

        <a href="manage_shifts.php" class="feature-card">
          <div class="card-icon shifts">
            <i class="bi bi-clock-fill"></i>
          </div>
          <div class="card-title">Manage Shifts</div>
          <div class="card-description">Create shift templates, assign shifts to employees, and manage work schedules efficiently.</div>
          <div class="card-footer">
            <span class="card-action">
              Create shifts <i class="bi bi-arrow-right"></i>
            </span>
          </div>
        </a>

        <a href="reports.php" class="feature-card">
          <div class="card-icon reports">
            <i class="bi bi-bar-chart-fill"></i>
          </div>
          <div class="card-title">Attendance Reports</div>
          <div class="card-description">Analyze employee attendance patterns, generate reports, and track productivity metrics.</div>
          <div class="card-footer">
            <span class="card-action">
              View reports <i class="bi bi-arrow-right"></i>
            </span>
          </div>
        </a>

      <?php else: ?>

        <a href="index.php" class="feature-card">
          <div class="card-icon clock">
            <i class="bi bi-check2-circle"></i>
          </div>
          <div class="card-title">Clock In/Out</div>
          <div class="card-description">Record your attendance with location tracking and photo verification for accurate time tracking.</div>
          <div class="card-footer">
            <span class="card-action">
              Clock in now <i class="bi bi-arrow-right"></i>
            </span>
          </div>
        </a>

        <a href="reports.php" class="feature-card">
          <div class="card-icon reports">
            <i class="bi bi-file-text-fill"></i>
          </div>
          <div class="card-title">My Reports</div>
          <div class="card-description">View your attendance history, track your work hours, and monitor your attendance patterns.</div>
          <div class="card-footer">
            <span class="card-action">
              View history <i class="bi bi-arrow-right"></i>
            </span>
          </div>
        </a>

      <?php endif; ?>
    </div>

    <!-- Logout Section -->
    <div class="logout-section">
      <a href="logout.php" class="logout-btn">
        <i class="bi bi-box-arrow-right"></i>
        Logout
      </a>
    </div>
  </div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
  // Enhanced Dashboard Functionality
  document.addEventListener('DOMContentLoaded', function() {
    // Add loading animation
    setTimeout(() => {
      document.querySelector('.dashboard-container').style.opacity = '1';
    }, 100);

    // Add click animations to cards
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach(card => {
      card.addEventListener('click', function(e) {
        // Create ripple effect
        const ripple = document.createElement('div');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.cssText = `
          position: absolute;
          width: ${size}px;
          height: ${size}px;
          left: ${x}px;
          top: ${y}px;
          background: rgba(102, 126, 234, 0.3);
          border-radius: 50%;
          transform: scale(0);
          animation: ripple 0.6s ease-out;
          pointer-events: none;
          z-index: 1;
        `;

        this.style.position = 'relative';
        this.appendChild(ripple);

        setTimeout(() => {
          ripple.remove();
        }, 600);
      });
    });

    // Add hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px) scale(1.02)';
      });

      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
      });
    });

    // Animate stat values on load
    const statValues = document.querySelectorAll('.stat-value');
    statValues.forEach(stat => {
      const finalValue = parseInt(stat.textContent);
      let currentValue = 0;
      const increment = finalValue / 30;

      const timer = setInterval(() => {
        currentValue += increment;
        if (currentValue >= finalValue) {
          stat.textContent = finalValue;
          clearInterval(timer);
        } else {
          stat.textContent = Math.floor(currentValue);
        }
      }, 50);
    });

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        // Focus on first card
        const firstCard = document.querySelector('.feature-card');
        if (firstCard) firstCard.focus();
      }
    });

    // Add focus styles for accessibility
    cards.forEach(card => {
      card.addEventListener('focus', function() {
        this.style.outline = '3px solid #667eea';
        this.style.outlineOffset = '2px';
      });

      card.addEventListener('blur', function() {
        this.style.outline = 'none';
      });
    });

    // Add time-based greeting
    const hour = new Date().getHours();
    const welcomeText = document.querySelector('.welcome-section h1');
    if (welcomeText) {
      let greeting = 'Welcome back';
      if (hour < 12) greeting = 'Good morning';
      else if (hour < 17) greeting = 'Good afternoon';
      else greeting = 'Good evening';

      welcomeText.innerHTML = welcomeText.innerHTML.replace('Welcome back', greeting);
    }

    // Add real-time clock
    function updateClock() {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: true,
        hour: 'numeric',
        minute: '2-digit'
      });

      const dateElement = document.querySelector('.welcome-section p');
      if (dateElement) {
        const currentText = dateElement.textContent;
        const parts = currentText.split(' • ');
        if (parts.length > 1) {
          dateElement.textContent = `${parts[0]} • ${parts[1]} • ${timeString}`;
        }
      }
    }

    updateClock();
    setInterval(updateClock, 60000); // Update every minute
  });

  // Add CSS for ripple animation
  const style = document.createElement('style');
  style.textContent = `
    @keyframes ripple {
      to {
        transform: scale(2);
        opacity: 0;
      }
    }

    .feature-card {
      overflow: hidden;
    }

    .feature-card:focus {
      outline: 3px solid #667eea !important;
      outline-offset: 2px !important;
    }
  `;
  document.head.appendChild(style);
</script>
</body>
</html>
