<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

$role = $_SESSION['role'];
$username = $_SESSION['username'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Dashboard - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <style>
    body {
      max-width: 900px;
      margin: auto;
      padding: 20px;
      background: #f8f9fa;
    }
    h1 {
      color: #0d6efd;
      margin-bottom: 40px;
      text-align: center;
      font-weight: 700;
    }
    .card {
      transition: transform 0.2s ease-in-out;
      cursor: pointer;
    }
    .card:hover {
      transform: scale(1.05);
      box-shadow: 0 0 15px rgba(0,0,0,0.15);
    }
    .logout-btn {
      margin-top: 40px;
      display: flex;
      justify-content: center;
    }
  </style>
</head>
<body>
  <h1>Welcome, <?=htmlspecialchars($username)?></h1>
  <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 g-4">

    <?php if ($role === 'admin'): ?>

      <div class="col">
        <a href="manage_employees.php" class="text-decoration-none text-dark">
          <div class="card text-center p-3 h-100">
            <i class="bi bi-people-fill display-1 text-primary"></i>
            <h5 class="mt-3">Manage Employees</h5>
            <p class="text-muted">Add or remove employees</p>
          </div>
        </a>
      </div>

      <div class="col">
        <a href="manage_shifts.php" class="text-decoration-none text-dark">
          <div class="card text-center p-3 h-100">
            <i class="bi bi-clock-fill display-1 text-success"></i>
            <h5 class="mt-3">Manage Shifts</h5>
            <p class="text-muted">Create & assign shifts</p>
          </div>
        </a>
      </div>

      <div class="col">
        <a href="reports.php" class="text-decoration-none text-dark">
          <div class="card text-center p-3 h-100">
            <i class="bi bi-bar-chart-fill display-1 text-warning"></i>
            <h5 class="mt-3">View Attendance Reports</h5>
            <p class="text-muted">Analyze employee attendance</p>
          </div>
        </a>
      </div>

    <?php else: ?>

      <div class="col">
        <a href="index.php" class="text-decoration-none text-dark">
          <div class="card text-center p-3 h-100">
            <i class="bi bi-check2-circle display-1 text-success"></i>
            <h5 class="mt-3">Clock In/Out</h5>
            <p class="text-muted">Record your attendance</p>
          </div>
        </a>
      </div>

      <div class="col">
        <a href="reports.php" class="text-decoration-none text-dark">
          <div class="card text-center p-3 h-100">
            <i class="bi bi-file-text-fill display-1 text-info"></i>
            <h5 class="mt-3">My Reports</h5>
            <p class="text-muted">View your attendance records</p>
          </div>
        </a>
      </div>

    <?php endif; ?>

  </div>

  <div class="logout-btn">
    <a href="logout.php" class="btn btn-outline-danger btn-lg mt-5">
      <i class="bi bi-box-arrow-right"></i> Logout
    </a>
  </div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
