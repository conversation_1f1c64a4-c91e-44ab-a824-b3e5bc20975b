<?php
session_start();
if (isset($_SESSION['user_id'])) {
  header("Location: dashboard.php");
  exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("DB connection failed.");
}

$error = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $username = trim($_POST['username'] ?? '');
  $password = $_POST['password'] ?? '';

  if ($username && $password) {
    $stmt = $mysqli->prepare("SELECT id, password_hash, role, employee_id FROM users WHERE username = ?");
    $stmt->bind_param('s', $username);
    $stmt->execute();
    $stmt->bind_result($id, $password_hash, $role, $employee_id);
    if ($stmt->fetch()) {
      $stmt->close();

      if (password_verify($password, $password_hash)) {
        $_SESSION['user_id'] = $id;
        $_SESSION['username'] = $username;
        $_SESSION['role'] = $role;
        $_SESSION['employee_id'] = $employee_id;
        header("Location: dashboard.php");
        exit;
      } else {
        $error = "Invalid username or password.";
      }
    } else {
      $error = "Invalid username or password.";
    }
  } else {
    $error = "Please enter username and password.";
  }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Login - Attendance App</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<style>body { max-width: 400px; margin:auto; padding-top: 40px; }</style>
</head>
<body>
<div class="container">
<h1 class="mb-4">Login</h1>

<?php if ($error): ?>
<div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
<?php endif; ?>

<form method="POST" action="login.php" novalidate>
  <div class="mb-3">
    <label for="username" class="form-label">Username</label>
    <input type="text" id="username" class="form-control" name="username" required autofocus />
  </div>
  <div class="mb-3">
    <label for="password" class="form-label">Password</label>
    <input type="password" id="password" class="form-control" name="password" required />
  </div>
  <button type="submit" class="btn btn-primary w-100">Login</button>
</form>
</div>
</body>
</html>
