<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
  header("Location: login.php");
  exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

$msg = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['add_employee'])) {
    $name = trim($_POST['name'] ?? '');
    if ($name) {
      $stmt = $mysqli->prepare("INSERT INTO employees (name) VALUES (?)");
      $stmt->bind_param('s', $name);
      if ($stmt->execute()) {
        $msg = "Employee added.";
      } else {
        $msg = "Failed to add employee.";
      }
      $stmt->close();
    } else {
      $msg = "Name cannot be empty.";
    }
  } elseif (isset($_POST['delete_employee']) && isset($_POST['employee_id'])) {
    $employee_id = intval($_POST['employee_id']);
    $stmt = $mysqli->prepare("DELETE FROM employees WHERE id = ?");
    $stmt->bind_param('i', $employee_id);
    if ($stmt->execute()) {
      $msg = "Employee deleted.";
    } else {
      $msg = "Failed to delete employee.";
    }
    $stmt->close();
  }
}

$result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = [];
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Manage Employees</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<style>body { max-width: 600px; margin: auto; padding: 10px; }</style>
</head>
<body>
<div class="container">
<h1>Manage Employees</h1>
<?php if ($msg): ?>
<div class="alert alert-info"><?= htmlspecialchars($msg) ?></div>
<?php endif; ?>

<form method="POST">
  <div class="mb-3">
    <label for="name" class="form-label">Add New Employee:</label>
    <input type="text" class="form-control" name="name" id="name" required placeholder="Employee Name" />
  </div>
  <button type="submit" name="add_employee" class="btn btn-primary mb-3">Add</button>
</form>

<h2>Existing Employees</h2>
<?php if (count($employees) === 0): ?>
<p>No employees found.</p>
<?php else: ?>
<table class="table table-bordered">
  <thead><tr><th>Name</th><th>Action</th></tr></thead>
  <tbody>
    <?php foreach ($employees as $emp): ?>
      <tr>
        <td><?= htmlspecialchars($emp['name']) ?></td>
        <td>
          <form method="POST" onsubmit="return confirm('Delete this employee? All related data will be removed.');" style="display:inline;">
            <input type="hidden" name="employee_id" value="<?= $emp['id'] ?>" />
            <button type="submit" name="delete_employee" class="btn btn-danger btn-sm">Delete</button>
          </form>
        </td>
      </tr>
    <?php endforeach; ?>
  </tbody>
</table>
<?php endif; ?>

<a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
</div>
</body>
</html>
