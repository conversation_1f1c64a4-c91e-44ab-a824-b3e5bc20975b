<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
  header("Location: login.php");
  exit;
}

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

$msg = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['add_employee'])) {
    $name = trim($_POST['name'] ?? '');
    if ($name) {
      $stmt = $mysqli->prepare("INSERT INTO employees (name) VALUES (?)");
      $stmt->bind_param('s', $name);
      if ($stmt->execute()) {
        $msg = "Employee added.";
      } else {
        $msg = "Failed to add employee.";
      }
      $stmt->close();
    } else {
      $msg = "Name cannot be empty.";
    }
  } elseif (isset($_POST['delete_employee']) && isset($_POST['employee_id'])) {
    $employee_id = intval($_POST['employee_id']);
    $stmt = $mysqli->prepare("DELETE FROM employees WHERE id = ?");
    $stmt->bind_param('i', $employee_id);
    if ($stmt->execute()) {
      $msg = "Employee deleted.";
    } else {
      $msg = "Failed to delete employee.";
    }
    $stmt->close();
  }
}

$result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = [];
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Manage Employees - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <?php include 'includes/navigation.php'; ?>

  <div class="page-container animate-fade-in">
    <!-- Page Header -->
    <div class="page-header hover-lift">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Manage Employees</h1>
          <p class="page-subtitle">Add, edit, and manage your organization's employees</p>
        </div>
        <div class="d-flex align-items-center gap-3">
          <div class="badge bg-primary">
            <?= count($employees) ?> Total Employees
          </div>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <!-- Add Employee Form -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-plus me-2"></i>
        Add New Employee
      </h2>
      <form method="POST">
        <div class="form-group">
          <label for="name" class="form-label">Employee Name</label>
          <input
            type="text"
            class="form-control"
            name="name"
            id="name"
            required
            placeholder="Enter employee full name"
          />
        </div>
        <button type="submit" name="add_employee" class="btn btn-primary">
          <i class="bi bi-plus-circle me-2"></i>
          Add Employee
        </button>
      </form>
    </div>

    <!-- Existing Employees -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-people me-2"></i>
        Existing Employees
        <span class="badge bg-primary ms-2"><?= count($employees) ?></span>
      </h2>

      <?php if (count($employees) === 0): ?>
        <div class="text-center py-5">
          <i class="bi bi-people" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No employees found. Add your first employee above.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>
                  <i class="bi bi-person me-2"></i>
                  Employee Name
                </th>
                <th width="150">
                  <i class="bi bi-gear me-2"></i>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($employees as $emp): ?>
                <tr>
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="avatar me-3">
                        <?= strtoupper(substr($emp['name'], 0, 1)) ?>
                      </div>
                      <div>
                        <strong><?= htmlspecialchars($emp['name']) ?></strong>
                        <br>
                        <small class="text-muted">ID: <?= $emp['id'] ?></small>
                      </div>
                    </div>
                  </td>
                  <td>
                    <form
                      method="POST"
                      onsubmit="return confirm('Are you sure you want to delete <?= htmlspecialchars($emp['name']) ?>? All related attendance data will be permanently removed.');"
                      style="display:inline;"
                    >
                      <input type="hidden" name="employee_id" value="<?= $emp['id'] ?>" />
                      <button type="submit" name="delete_employee" class="btn btn-danger btn-sm">
                        <i class="bi bi-trash me-1"></i>
                        Delete
                      </button>
                    </form>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>
  </div>

  <style>
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.1rem;
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
