<?php
define('GRACE_PERIOD_MINUTES', 5);

function timeToMinutes($time) {
    $parts = explode(":", $time);
    return intval($parts[0]) * 60 + intval($parts[1]);
}

function minutesToTime($minutes) {
    $h = floor($minutes / 60);
    $m = $minutes % 60;
    return sprintf("%02d:%02d", $h, $m);
}

function datetimeToMinutes($datetimeStr) {
    $dt = new DateTime($datetimeStr);
    return $dt->format('H') * 60 + $dt->format('i');
}

function diffMinutes($start, $end) {
    $startObj = new DateTime($start);
    $endObj = new DateTime($end);
    return max(0, ($endObj->getTimestamp() - $startObj->getTimestamp()) / 60);
}

function getShiftBreaks($mysqli, $shift_template_id) {
    $breaks = [];
    $stmt = $mysqli->prepare("SELECT break_start, break_end FROM shift_breaks WHERE shift_template_id = ?");
    $stmt->bind_param("i", $shift_template_id);
    $stmt->execute();
    $stmt->bind_result($bstart, $bend);
    while ($stmt->fetch()) {
        $breaks[] = ['start' => $bstart, 'end' => $bend];
    }
    $stmt->close();
    return $breaks;
}

function analyzeAttendance($shiftAssignments, $attendanceRecords) {
    $results = [];

    foreach ($shiftAssignments as $shift) {
        $shiftStart = timeToMinutes(substr($shift['start'],0,5));
        $shiftEnd = timeToMinutes(substr($shift['end'],0,5));
        $shiftName = $shift['name'];
        $shiftBreaks = $shift['breaks'];

        $clockInRecord = null;
        $clockOutRecord = null;

        foreach ($attendanceRecords as $idx => $rec) {
            if ($rec['type'] === 'in') {
                $clockInMins = datetimeToMinutes($rec['timestamp']);
                if ($clockInMins >= $shiftStart - GRACE_PERIOD_MINUTES && $clockInMins <= $shiftEnd) {
                    $clockInRecord = $rec;
                    for ($j = $idx +1; $j < count($attendanceRecords); $j++) {
                        if ($attendanceRecords[$j]['type'] === 'out') {
                            $clockOutRecord = $attendanceRecords[$j];
                            break;
                        }
                    }
                    break;
                }
            }
        }

        if (!$clockInRecord) {
            $results[] = [
                'shift_name' => $shiftName,
                'status' => 'Absent',
                'clock_in' => null,
                'clock_out' => null,
                'overtime' => 0,
                'work_minutes' => 0,
            ];
            continue;
        }

        $clockInMins = datetimeToMinutes($clockInRecord['timestamp']);
        if ($clockInMins <= $shiftStart + GRACE_PERIOD_MINUTES) {
            $status = 'On Time';
        } else {
            $status = 'Late';
        }

        $workMinutes = 0;
        if ($clockOutRecord) {
            $workMinutes = diffMinutes($clockInRecord['timestamp'], $clockOutRecord['timestamp']);
            $totalBreakMins = 0;
            foreach ($shiftBreaks as $b) {
                $bStartMins = timeToMinutes($b['start']);
                $bEndMins = timeToMinutes($b['end']);
                $start_overlap = max($clockInMins, $bStartMins);
                $end_overlap = min(datetimeToMinutes($clockOutRecord['timestamp']), $bEndMins);
                $overlap = max(0, $end_overlap - $start_overlap);
                $totalBreakMins += $overlap;
            }
            $workMinutes -= $totalBreakMins;
        }

        $overtime = 0;
        if ($clockOutRecord) {
            $clockOutMins = datetimeToMinutes($clockOutRecord['timestamp']);
            if ($clockOutMins > $shiftEnd) {
                $overtime = $clockOutMins - $shiftEnd;
            }
        }

        $results[] = [
            'shift_name' => $shiftName,
            'status' => $status,
            'clock_in' => $clockInRecord,
            'clock_out' => $clockOutRecord,
            'work_minutes' => max(0, $workMinutes),
            'overtime' => $overtime,
        ];
    }

    return $results;
}
