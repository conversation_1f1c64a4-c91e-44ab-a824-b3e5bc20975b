<?php
session_start();
if (!isset($_SESSION['user_id'])) {
  header("Location: login.php");
  exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

$role = $_SESSION['role'];
$employee_id_session = $_SESSION['employee_id'] ?? 0;

// Fetch employees for dropdown (admin only)
$employees = [];
$result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
while ($row = $result->fetch_assoc()) {
  $employees[] = $row;
}

// Selected employee/date 
if ($role === 'admin') {
  $selected_employee = isset($_GET['employee_id']) ? intval($_GET['employee_id']) : 0;
  if (!$selected_employee && !empty($employees)) {
    $selected_employee = $employees[0]['id'];
  }
} else {
  $selected_employee = $employee_id_session;
}
$selected_date = $_GET['date'] ?? date('Y-m-d');

// Retrieve assigned shifts for this employee/date (date in assigned range)
$stmt = $mysqli->prepare("
  SELECT st.id, st.name, st.start_time, st.end_time
  FROM employee_shifts es
  JOIN shift_templates st ON es.shift_template_id = st.id
  WHERE es.employee_id = ? AND ? BETWEEN es.shift_start_date AND es.shift_end_date
");
$stmt->bind_param("is", $selected_employee, $selected_date);
$stmt->execute();
$stmt->bind_result($st_id, $st_name, $st_start, $st_end);

$shiftAssignments = [];
while ($stmt->fetch()) {
  $shiftAssignments[] = [
    'id' => $st_id,
    'name' => $st_name,
    'start' => $st_start,
    'end' => $st_end,
    'breaks' => [],
  ];
}
$stmt->close();

foreach ($shiftAssignments as &$shift) {
  $shift['breaks'] = getShiftBreaks($mysqli, $shift['id']);
}
unset($shift);

// Fetch attendance records
$stmt = $mysqli->prepare("
  SELECT type, timestamp, selfie_path 
  FROM attendance 
  WHERE employee_id = ? AND DATE(timestamp) = ?
  ORDER BY timestamp ASC
");
$stmt->bind_param('is', $selected_employee, $selected_date);
$stmt->execute();
$stmt->bind_result($type, $timestamp, $selfie_path);

$attendanceRecords = [];
while ($stmt->fetch()) {
  $attendanceRecords[] = [
    'type' => $type,
    'timestamp' => $timestamp,
    'selfie' => $selfie_path,
  ];
}
$stmt->close();

$attendanceAnalysis = analyzeAttendance($shiftAssignments, $attendanceRecords);

$selectedEmployeeName = '';
foreach ($employees as $emp) {
  if ($emp['id'] === $selected_employee) {
    $selectedEmployeeName = $emp['name'];
    break;
  }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>Attendance Reports - Attendance App</title>
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
<style>
  body {
    max-width: 900px; margin: 20px auto; background: #f8f9fa; padding: 15px;
  }
  .selfie-img {
    max-width: 120px; border-radius: 8px; margin-right: 10px;
  }
  .status {
    font-weight: 600;
    font-size: 1.2rem;
  }
  .status.On\: Time { color: blue; }
  .status.Early { color: green; }
  .status.Late { color: red; }
  .status.Absent { color: gray; }
</style>
</head>
<body>
<div class="container">
  <h1 class="mb-4">Attendance Reports</h1>

  <form method="GET" action="reports.php" class="row g-3 mb-4">
    <?php if ($role === 'admin'): ?>
      <div class="col-md-5">
        <label for="employee_id" class="form-label">Select Employee</label>
        <select id="employee_id" name="employee_id" class="form-select" required>
          <?php foreach ($employees as $emp): ?>
            <option value="<?= $emp['id'] ?>" <?= $emp['id'] == $selected_employee ? 'selected' : '' ?>>
              <?= htmlspecialchars($emp['name']) ?>
            </option>
          <?php endforeach; ?>
        </select>
      </div>
    <?php endif; ?>

    <div class="col-md-5">
      <label for="date" class="form-label">Select Date</label>
      <input type="date" id="date" name="date" value="<?= htmlspecialchars($selected_date) ?>" class="form-control" required />
    </div>

    <div class="col-md-2 d-flex align-items-end">
      <button type="submit" class="btn btn-primary w-100">View Report</button>
    </div>
  </form>

  <?php if (!$selected_employee): ?>
    <div class="alert alert-warning">Please select an employee.</div>
  <?php else: ?>

    <h2 class="mb-3">Report for <?= htmlspecialchars($selectedEmployeeName) ?> on <?= htmlspecialchars($selected_date) ?></h2>

    <?php if (empty($shiftAssignments)): ?>
      <div class="alert alert-info">No shift assigned for this date.</div>
    <?php else: ?>
      <?php foreach ($attendanceAnalysis as $shiftResult): ?>
        <div class="card p-3 mb-4">
          <h5><?= htmlspecialchars($shiftResult['shift_name']) ?></h5>
          <p>Status: <span class="status <?= str_replace(' ', '\\ ', htmlspecialchars($shiftResult['status'])) ?>"><?= htmlspecialchars($shiftResult['status']) ?></span></p>
          <p>
            <strong>Clock In:</strong> <?= $shiftResult['clock_in'] ? htmlspecialchars($shiftResult['clock_in']['timestamp']) : '<em>Absent</em>' ?><br>
            <strong>Clock Out:</strong> <?= $shiftResult['clock_out'] ? htmlspecialchars($shiftResult['clock_out']['timestamp']) : '<em>Not recorded</em>' ?>
          </p>
          <p>Work Time: <?= intval($shiftResult['work_minutes']) ?> minutes</p>
          <p>Overtime: <?= intval($shiftResult['overtime']) ?> minutes</p>

          <div class="d-flex align-items-center">
            <?php if ($shiftResult['clock_in'] && $shiftResult['clock_in']['selfie']): ?>
              <img src="<?= htmlspecialchars($shiftResult['clock_in']['selfie']) ?>" alt="Clock In Selfie" class="selfie-img" />
            <?php endif; ?>
            <?php if ($shiftResult['clock_out'] && $shiftResult['clock_out']['selfie']): ?>
              <img src="<?= htmlspecialchars($shiftResult['clock_out']['selfie']) ?>" alt="Clock Out Selfie" class="selfie-img" />
            <?php endif; ?>
          </div>

          <?php if (!empty($shiftResult['breaks'])): ?>
            <h6 class="mt-3">Breaks</h6>
            <ul>
              <?php foreach ($shiftResult['breaks'] as $break): ?>
                <li><?= htmlspecialchars($break['start']) ?> - <?= htmlspecialchars($break['end']) ?></li>
              <?php endforeach; ?>
            </ul>
          <?php endif; ?>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>

  <?php endif; ?>

  <a href="dashboard.php" class="btn btn-secondary mt-4">Back to Dashboard</a>
</div>
</body>
</html>
