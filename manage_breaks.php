<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit;
}

$host = "localhost";
$user = "root";
$pass = "";
$db = "attendance_db2";

$mysqli = new mysqli($host, $user, $pass, $db);
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

$msg = '';
$error = '';

// POST handling for add/delete breaks
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add_break') {
        $template_id = intval($_POST['template_id'] ?? 0);
        $break_start = $_POST['break_start'] ?? '';
        $break_end = $_POST['break_end'] ?? '';
        $description = trim($_POST['description'] ?? '');

        if ($template_id && $break_start && $break_end) {
            if ($break_start >= $break_end) {
                $error = "Break start time must be before end time.";
            } else {
                $stmt = $mysqli->prepare("INSERT INTO shift_breaks (shift_template_id, break_start, break_end, description) VALUES (?, ?, ?, ?)");
                $stmt->bind_param("isss", $template_id, $break_start, $break_end, $description);
                if ($stmt->execute()) {
                    $msg = "Break added successfully.";
                } else {
                    $error = "Failed to add break.";
                }
                $stmt->close();
            }
        } else {
            $error = "Please fill all the break time fields.";
        }
    } elseif ($action === 'delete_break') {
        $break_id = intval($_POST['break_id'] ?? 0);
        if ($break_id) {
            $stmt = $mysqli->prepare("DELETE FROM shift_breaks WHERE id = ?");
            $stmt->bind_param("i", $break_id);
            if ($stmt->execute()) {
                $msg = "Break deleted successfully.";
            } else {
                $error = "Failed to delete break.";
            }
            $stmt->close();
        }
    }
}

// Get all shift templates for dropdown
$templates = [];
$result = $mysqli->query("SELECT id, name FROM shift_templates ORDER BY name");
while ($row = $result->fetch_assoc()) {
    $templates[] = $row;
}

// Selected shift template (from POST first, then GET, else first template)
$selected_template_id = 0;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_template_id = intval($_POST['template_id'] ?? 0);
} elseif (isset($_GET['template_id'])) {
    $selected_template_id = intval($_GET['template_id']);
}

if (!$selected_template_id && count($templates) > 0) {
    $selected_template_id = $templates[0]['id'];
}

// Get breaks for selected shift template
$breaks = [];
if ($selected_template_id) {
    $stmt = $mysqli->prepare("SELECT id, break_start, break_end, description FROM shift_breaks WHERE shift_template_id = ? ORDER BY break_start");
    $stmt->bind_param("i", $selected_template_id);
    $stmt->execute();
    $stmt->bind_result($bid, $bstart, $bend, $bdesc);
    while ($stmt->fetch()) {
        $breaks[] = ['id' => $bid, 'start' => $bstart, 'end' => $bend, 'description' => $bdesc];
    }
    $stmt->close();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Manage Shift Breaks - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Manage Shift Breaks</h1>
          <p class="page-subtitle">Configure break times for different shift templates</p>
        </div>
        <div>
          <a href="dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>
    <?php if ($error): ?>
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?= htmlspecialchars($error) ?>
      </div>
    <?php endif; ?>

    <!-- Shift Template Selection -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-clock me-2"></i>
        Select Shift Template
      </h2>
      <form method="POST" onchange="this.submit()" aria-label="Select shift template">
        <input type="hidden" name="action" value="select_template" />
        <div class="form-group">
          <label for="template_id" class="form-label">
            <i class="bi bi-list me-2"></i>
            Choose a shift template to manage breaks
          </label>
          <select name="template_id" id="template_id" class="form-select">
            <?php foreach ($templates as $template): ?>
              <option value="<?= $template['id'] ?>" <?= $template['id'] == $selected_template_id ? 'selected' : '' ?>>
                <?= htmlspecialchars($template['name']) ?>
              </option>
            <?php endforeach; ?>
          </select>
        </div>
      </form>
    </div>

    <!-- Breaks Management -->
    <?php if (!$selected_template_id): ?>
      <div class="content-card">
        <div class="text-center py-5">
          <i class="bi bi-exclamation-circle" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No shift templates found. Please create a shift template first.</p>
          <a href="manage_shifts.php" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>
            Create Shift Template
          </a>
        </div>
      </div>
    <?php else: ?>

      <!-- Current Breaks -->
      <div class="content-card">
        <h2 class="card-title">
          <i class="bi bi-pause-circle me-2"></i>
          Breaks for <?= htmlspecialchars($templates[array_search($selected_template_id, array_column($templates, 'id'))]['name'] ?? '') ?>
          <span class="badge bg-primary ms-2"><?= count($breaks) ?></span>
        </h2>

        <?php if (empty($breaks)): ?>
          <div class="text-center py-4">
            <i class="bi bi-pause-circle" style="font-size: 2.5rem; color: var(--text-secondary);"></i>
            <p class="mt-3 text-muted">No breaks defined for this shift template.</p>
          </div>
        <?php else: ?>
          <div class="breaks-list">
            <?php foreach ($breaks as $b): ?>
              <div class="break-item">
                <div class="break-info">
                  <div class="break-time">
                    <i class="bi bi-clock me-2"></i>
                    <span class="time-badge start-time"><?= htmlspecialchars($b['start']) ?></span>
                    <span class="mx-2">to</span>
                    <span class="time-badge end-time"><?= htmlspecialchars($b['end']) ?></span>
                  </div>
                  <?php if ($b['description']): ?>
                    <div class="break-description">
                      <i class="bi bi-info-circle me-2"></i>
                      <?= htmlspecialchars($b['description']) ?>
                    </div>
                  <?php endif; ?>
                </div>
                <form method="POST" style="margin:0;" onsubmit="return confirm('Are you sure you want to delete this break?');">
                  <input type="hidden" name="action" value="delete_break" />
                  <input type="hidden" name="break_id" value="<?= $b['id'] ?>" />
                  <input type="hidden" name="template_id" value="<?= $selected_template_id ?>" />
                  <button type="submit" class="btn btn-danger btn-sm" aria-label="Delete break">
                    <i class="bi bi-trash me-1"></i>
                    Delete
                  </button>
                </form>
              </div>
            <?php endforeach; ?>
          </div>
        <?php endif; ?>
      </div>

      <!-- Add New Break -->
      <div class="content-card">
        <h2 class="card-title">
          <i class="bi bi-plus-circle me-2"></i>
          Add New Break
        </h2>
        <form method="POST" aria-label="Add break form">
          <input type="hidden" name="action" value="add_break" />
          <input type="hidden" name="template_id" value="<?= $selected_template_id ?>" />

          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="break_start" class="form-label">
                  <i class="bi bi-clock me-2"></i>
                  Start Time
                </label>
                <input type="time" id="break_start" name="break_start" class="form-control" required />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="break_end" class="form-label">
                  <i class="bi bi-clock-history me-2"></i>
                  End Time
                </label>
                <input type="time" id="break_end" name="break_end" class="form-control" required />
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-group">
                <label for="description" class="form-label">
                  <i class="bi bi-file-text me-2"></i>
                  Description (Optional)
                </label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  class="form-control"
                  placeholder="e.g. Lunch break, Coffee break"
                />
              </div>
            </div>
          </div>

          <button type="submit" class="btn btn-primary" aria-label="Add Break">
            <i class="bi bi-plus-circle me-2"></i>
            Add Break
          </button>
        </form>
      </div>
    <?php endif; ?>
  </div>

  <style>
    .breaks-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .break-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 1.25rem;
      border: 1px solid var(--border-color);
      display: flex;
      justify-content: between;
      align-items: center;
      gap: 1rem;
    }

    .break-info {
      flex: 1;
    }

    .break-time {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .break-description {
      display: flex;
      align-items: center;
      color: var(--text-secondary);
      font-size: 0.875rem;
    }

    .time-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
      font-family: 'Courier New', monospace;
    }

    .start-time {
      background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
      color: #059669;
      border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .end-time {
      background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
      color: #dc2626;
      border: 1px solid rgba(239, 68, 68, 0.2);
    }

    @media (max-width: 768px) {
      .break-item {
        flex-direction: column;
        align-items: flex-start;
      }

      .time-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Form validation
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.querySelector('form[aria-label="Add break form"]');
      if (form) {
        form.addEventListener('submit', function(e) {
          const startTime = document.getElementById('break_start').value;
          const endTime = document.getElementById('break_end').value;

          if (startTime && endTime && startTime >= endTime) {
            e.preventDefault();
            alert('Break start time must be before end time.');
            return false;
          }
        });
      }

      // Real-time validation
      const inputs = document.querySelectorAll('input[required]');
      inputs.forEach(input => {
        input.addEventListener('blur', function() {
          if (this.value.trim()) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
          } else {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
          }
        });
      });
    });
  </script>
</body>
</html>