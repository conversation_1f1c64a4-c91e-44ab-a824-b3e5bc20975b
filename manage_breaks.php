<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: login.php");
    exit;
}

$host = "localhost";
$user = "root";
$pass = "";
$db = "attendance_db2";

$mysqli = new mysqli($host, $user, $pass, $db);
if ($mysqli->connect_errno) {
    die("Database connection failed.");
}

$msg = '';
$error = '';

// POST handling for add/delete breaks
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'add_break') {
        $template_id = intval($_POST['template_id'] ?? 0);
        $break_start = $_POST['break_start'] ?? '';
        $break_end = $_POST['break_end'] ?? '';
        $description = trim($_POST['description'] ?? '');

        if ($template_id && $break_start && $break_end) {
            if ($break_start >= $break_end) {
                $error = "Break start time must be before end time.";
            } else {
                $stmt = $mysqli->prepare("INSERT INTO shift_breaks (shift_template_id, break_start, break_end, description) VALUES (?, ?, ?, ?)");
                $stmt->bind_param("isss", $template_id, $break_start, $break_end, $description);
                if ($stmt->execute()) {
                    $msg = "Break added successfully.";
                } else {
                    $error = "Failed to add break.";
                }
                $stmt->close();
            }
        } else {
            $error = "Please fill all the break time fields.";
        }
    } elseif ($action === 'delete_break') {
        $break_id = intval($_POST['break_id'] ?? 0);
        if ($break_id) {
            $stmt = $mysqli->prepare("DELETE FROM shift_breaks WHERE id = ?");
            $stmt->bind_param("i", $break_id);
            if ($stmt->execute()) {
                $msg = "Break deleted successfully.";
            } else {
                $error = "Failed to delete break.";
            }
            $stmt->close();
        }
    }
}

// Get all shift templates for dropdown
$templates = [];
$result = $mysqli->query("SELECT id, name FROM shift_templates ORDER BY name");
while ($row = $result->fetch_assoc()) {
    $templates[] = $row;
}

// Selected shift template (from POST first, then GET, else first template)
$selected_template_id = 0;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_template_id = intval($_POST['template_id'] ?? 0);
} elseif (isset($_GET['template_id'])) {
    $selected_template_id = intval($_GET['template_id']);
}

if (!$selected_template_id && count($templates) > 0) {
    $selected_template_id = $templates[0]['id'];
}

// Get breaks for selected shift template
$breaks = [];
if ($selected_template_id) {
    $stmt = $mysqli->prepare("SELECT id, break_start, break_end, description FROM shift_breaks WHERE shift_template_id = ? ORDER BY break_start");
    $stmt->bind_param("i", $selected_template_id);
    $stmt->execute();
    $stmt->bind_result($bid, $bstart, $bend, $bdesc);
    while ($stmt->fetch()) {
        $breaks[] = ['id' => $bid, 'start' => $bstart, 'end' => $bend, 'description' => $bdesc];
    }
    $stmt->close();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Manage Shift Breaks - Attendance App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
</head>
<body>
<div class="container my-4">
    <h1>Manage Shift Breaks</h1>

    <?php if ($msg): ?>
        <div class="alert alert-success"><?= htmlspecialchars($msg) ?></div>
    <?php endif; ?>
    <?php if ($error): ?>
        <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
    <?php endif; ?>

    <form method="POST" class="mb-4" onchange="this.submit()" aria-label="Select shift template">
        <input type="hidden" name="action" value="select_template" />
        <label for="template_id" class="form-label">Select Shift Template</label>
        <select name="template_id" id="template_id" class="form-select">
            <?php foreach ($templates as $template): ?>
                <option value="<?= $template['id'] ?>" <?= $template['id'] == $selected_template_id ? 'selected' : '' ?>>
                    <?= htmlspecialchars($template['name']) ?>
                </option>
            <?php endforeach; ?>
        </select>
    </form>

    <?php if (!$selected_template_id): ?>
        <p>No shift templates found.</p>
    <?php else: ?>
        <h3>Breaks for <?= htmlspecialchars($templates[array_search($selected_template_id, array_column($templates, 'id'))]['name'] ?? '') ?></h3>

        <?php if (empty($breaks)): ?>
            <p>No breaks defined.</p>
        <?php else: ?>
            <ul class="list-group mb-3">
                <?php foreach ($breaks as $b): ?>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?= htmlspecialchars($b['start']) ?> - <?= htmlspecialchars($b['end']) ?> <?= $b['description'] ? '('.htmlspecialchars($b['description']).')' : '' ?>
                        <form method="POST" style="margin:0;">
                            <input type="hidden" name="action" value="delete_break" />
                            <input type="hidden" name="break_id" value="<?= $b['id'] ?>" />
                            <input type="hidden" name="template_id" value="<?= $selected_template_id ?>" />
                            <button type="submit" class="btn btn-sm btn-danger" aria-label="Delete break">Delete</button>
                        </form>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

        <h4>Add a Break</h4>
        <form method="POST" class="row g-3" aria-label="Add break form">
            <input type="hidden" name="action" value="add_break" />
            <input type="hidden" name="template_id" value="<?= $selected_template_id ?>" />

            <div class="col-md-3">
                <label for="break_start" class="form-label">Start Time</label>
                <input type="time" id="break_start" name="break_start" class="form-control" required />
            </div>
            <div class="col-md-3">
                <label for="break_end" class="form-label">End Time</label>
                <input type="time" id="break_end" name="break_end" class="form-control" required />
            </div>
            <div class="col-md-4">
                <label for="description" class="form-label">Description</label>
                <input type="text" id="description" name="description" class="form-control" placeholder="Optional" />
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100" aria-label="Add Break">Add Break</button>
            </div>
        </form>
    <?php endif; ?>

    <a href="dashboard.php" class="btn btn-secondary mt-4">Back to Dashboard</a>
</div>
</body>
</html>