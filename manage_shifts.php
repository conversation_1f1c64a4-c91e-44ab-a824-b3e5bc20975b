<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
  header("Location: login.php");
  exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

$msg = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_template') {
      $name = trim($_POST['name'] ?? '');
      $start_time = $_POST['start_time'] ?? '';
      $end_time = $_POST['end_time'] ?? '';
      $desc = trim($_POST['description'] ?? '');

      if ($name && $start_time && $end_time) {
        $stmt = $mysqli->prepare("INSERT INTO shift_templates (name, start_time, end_time, description) VALUES (?, ?, ?, ?)");
        $stmt->bind_param('ssss', $name, $start_time, $end_time, $desc);
        if ($stmt->execute()) {
          $msg = "Shift template \"$name\" added.";
        } else {
          $msg = "Failed to add shift template (it may already exist).";
        }
        $stmt->close();
      } else {
        $msg = "Please fill all template fields.";
      }
    } elseif ($action === 'assign_shift') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $shift_date = $_POST['shift_date'] ?? '';

      if ($employee_id && $shift_template_id && $shift_date) {
        $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_date) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE shift_template_id = VALUES(shift_template_id)");
        $stmt->bind_param('iis', $employee_id, $shift_template_id, $shift_date);
        if ($stmt->execute()) {
          $msg = "Assigned shift to employee.";
        } else {
          $msg = "Failed to assign shift.";
        }
        $stmt->close();
      } else {
        $msg = "Please select employee, shift template, and date to assign.";
      }
    } elseif ($action === 'delete_template') {
      $template_id = intval($_POST['template_id'] ?? 0);
      if ($template_id) {
        $stmt = $mysqli->prepare("DELETE FROM shift_templates WHERE id = ?");
        $stmt->bind_param('i', $template_id);
        if ($stmt->execute()) {
          $msg = "Shift template deleted.";
        } else {
          $msg = "Failed to delete shift template.";
        }
        $stmt->close();
      }
    }
  }
}

// Fetch shift templates
$templates_result = $mysqli->query("SELECT * FROM shift_templates ORDER BY name");
$shift_templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Fetch employees
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Manage Shifts - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.9.1/font/bootstrap-icons.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
  <link rel="stylesheet" href="style.css" />
</head>
<body>
  <div class="page-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h1 class="page-title">Manage Shifts</h1>
          <p class="page-subtitle">Create shift templates and assign them to employees</p>
        </div>
        <div>
          <a href="dashboard.php" class="nav-link">
            <i class="bi bi-arrow-left"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Messages -->
    <?php if ($msg): ?>
      <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?= htmlspecialchars($msg) ?>
      </div>
    <?php endif; ?>

    <!-- Add Shift Template -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-plus-circle me-2"></i>
        Add Shift Template
      </h2>
      <form method="POST">
        <input type="hidden" name="action" value="add_template" />

        <div class="form-group">
          <label for="name" class="form-label">
            <i class="bi bi-tag me-2"></i>
            Template Name
          </label>
          <input
            type="text"
            class="form-control"
            id="name"
            name="name"
            placeholder="e.g. Day Shift, Night Shift, Weekend Shift"
            required
          />
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="start_time" class="form-label">
                <i class="bi bi-clock me-2"></i>
                Start Time
              </label>
              <input type="time" class="form-control" id="start_time" name="start_time" required />
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="end_time" class="form-label">
                <i class="bi bi-clock-history me-2"></i>
                End Time
              </label>
              <input type="time" class="form-control" id="end_time" name="end_time" required />
            </div>
          </div>
        </div>

        <div class="form-group">
          <label for="description" class="form-label">
            <i class="bi bi-file-text me-2"></i>
            Description (optional)
          </label>
          <textarea
            class="form-control"
            id="description"
            name="description"
            rows="3"
            placeholder="Add any additional details about this shift..."
          ></textarea>
        </div>

        <button type="submit" class="btn btn-primary">
          <i class="bi bi-plus-circle me-2"></i>
          Add Template
        </button>
      </form>
    </div>

    <!-- Existing Shift Templates -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-list me-2"></i>
        Existing Shift Templates
        <span class="badge bg-primary ms-2"><?= count($shift_templates) ?></span>
      </h2>

      <?php if (empty($shift_templates)): ?>
        <div class="text-center py-5">
          <i class="bi bi-clock" style="font-size: 3rem; color: var(--text-secondary);"></i>
          <p class="mt-3 text-muted">No shift templates created yet. Add your first template above.</p>
        </div>
      <?php else: ?>
        <div class="table-container">
          <table class="table">
            <thead>
              <tr>
                <th>
                  <i class="bi bi-tag me-2"></i>
                  Name
                </th>
                <th>
                  <i class="bi bi-clock me-2"></i>
                  Start Time
                </th>
                <th>
                  <i class="bi bi-clock-history me-2"></i>
                  End Time
                </th>
                <th>
                  <i class="bi bi-file-text me-2"></i>
                  Description
                </th>
                <th width="120">
                  <i class="bi bi-gear me-2"></i>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
            <?php foreach ($shift_templates as $tpl): ?>
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    <div class="shift-icon me-3">
                      <i class="bi bi-clock"></i>
                    </div>
                    <strong><?= htmlspecialchars($tpl['name']) ?></strong>
                  </div>
                </td>
                <td>
                  <span class="time-badge start-time">
                    <?= htmlspecialchars(substr($tpl['start_time'], 0, 5)) ?>
                  </span>
                </td>
                <td>
                  <span class="time-badge end-time">
                    <?= htmlspecialchars(substr($tpl['end_time'], 0, 5)) ?>
                  </span>
                </td>
                <td>
                  <small class="text-muted">
                    <?= $tpl['description'] ? nl2br(htmlspecialchars($tpl['description'])) : '<em>No description</em>' ?>
                  </small>
                </td>
                <td>
                  <form method="POST" style="display:inline;" onsubmit="return confirm('Are you sure you want to delete the shift template \'<?= htmlspecialchars($tpl['name']) ?>\'?');">
                    <input type="hidden" name="action" value="delete_template" />
                    <input type="hidden" name="template_id" value="<?= intval($tpl['id']) ?>" />
                    <button type="submit" class="btn btn-danger btn-sm">
                      <i class="bi bi-trash me-1"></i>
                      Delete
                    </button>
                  </form>
                </td>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      <?php endif; ?>
    </div>

    <!-- Assign Shift to Employee -->
    <div class="content-card">
      <h2 class="card-title">
        <i class="bi bi-person-plus me-2"></i>
        Assign Shift to Employee
      </h2>
      <form method="POST">
        <input type="hidden" name="action" value="assign_shift" />

        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label for="employee_id" class="form-label">
                <i class="bi bi-person me-2"></i>
                Employee
              </label>
              <select id="employee_id" name="employee_id" class="form-select" required>
                <option value="">Choose an employee</option>
                <?php foreach ($employees as $emp): ?>
                  <option value="<?= intval($emp['id']) ?>"><?= htmlspecialchars($emp['name']) ?></option>
                <?php endforeach; ?>
              </select>
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-group">
              <label for="shift_template_id" class="form-label">
                <i class="bi bi-clock me-2"></i>
                Shift Template
              </label>
              <select id="shift_template_id" name="shift_template_id" class="form-select" required>
                <option value="">Choose a shift template</option>
                <?php foreach ($shift_templates as $tpl): ?>
                  <option value="<?= intval($tpl['id']) ?>">
                    <?= htmlspecialchars($tpl['name']) ?> (<?= substr($tpl['start_time'], 0, 5) ?> - <?= substr($tpl['end_time'], 0, 5) ?>)
                  </option>
                <?php endforeach; ?>
              </select>
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-group">
              <label for="date_range" class="form-label">
                <i class="bi bi-calendar-range me-2"></i>
                Select Date Range
              </label>
              <input
                type="text"
                id="date_range"
                name="date_range"
                class="form-control"
                placeholder="Click to select date range"
                required
              />
            </div>
          </div>
        </div>

        <button type="submit" class="btn btn-success">
          <i class="bi bi-check-circle me-2"></i>
          Assign Shift
        </button>
      </form>
    </div>
  </div>

  <style>
    .shift-icon {
      width: 35px;
      height: 35px;
      border-radius: 50%;
      background: var(--primary-gradient);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1rem;
    }

    .time-badge {
      display: inline-block;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.875rem;
      font-weight: 500;
      font-family: 'Courier New', monospace;
    }

    .start-time {
      background: linear-gradient(135deg, rgba(67, 233, 123, 0.1) 0%, rgba(56, 249, 215, 0.1) 100%);
      color: #059669;
      border: 1px solid rgba(16, 185, 129, 0.2);
    }

    .end-time {
      background: linear-gradient(135deg, rgba(250, 112, 154, 0.1) 0%, rgba(254, 225, 64, 0.1) 100%);
      color: #dc2626;
      border: 1px solid rgba(239, 68, 68, 0.2);
    }

    .badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }

    /* Flatpickr custom styling */
    .flatpickr-input {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='%23667eea' d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v1h14V3a1 1 0 0 0-1-1H2zm13 3H1v9a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V5z'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 0.75rem center;
      background-size: 16px 12px;
      padding-right: 2.5rem;
    }

    /* Form enhancements */
    .form-group {
      margin-bottom: 1.5rem;
    }

    @media (max-width: 768px) {
      .time-badge {
        font-size: 0.75rem;
        padding: 0.2rem 0.5rem;
      }

      .shift-icon {
        width: 30px;
        height: 30px;
        font-size: 0.875rem;
      }
    }
  </style>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
  <script>
    // Initialize Flatpickr for date range selection
    flatpickr("#date_range", {
      mode: "range",
      dateFormat: "Y-m-d",
      minDate: "today",
      theme: "light",
      locale: {
        rangeSeparator: " to "
      }
    });

    // Add form validation feedback
    document.addEventListener('DOMContentLoaded', function() {
      const forms = document.querySelectorAll('form');
      forms.forEach(form => {
        form.addEventListener('submit', function(e) {
          const requiredFields = form.querySelectorAll('[required]');
          let isValid = true;

          requiredFields.forEach(field => {
            if (!field.value.trim()) {
              field.classList.add('is-invalid');
              isValid = false;
            } else {
              field.classList.remove('is-invalid');
            }
          });

          if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
          }
        });
      });

      // Real-time validation
      const inputs = document.querySelectorAll('input[required], select[required], textarea[required]');
      inputs.forEach(input => {
        input.addEventListener('blur', function() {
          if (this.value.trim()) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
          } else {
            this.classList.add('is-invalid');
            this.classList.remove('is-valid');
          }
        });
      });
    });
  </script>
</body>
</html>
