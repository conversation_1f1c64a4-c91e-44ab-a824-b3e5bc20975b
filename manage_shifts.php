<?php
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
  header("Location: login.php");
  exit;
}

require_once __DIR__ . '/includes/attendance_helpers.php';

$mysqli = new mysqli("localhost", "root", "", "attendance_db2");
if ($mysqli->connect_errno) {
  die("Database connection failed.");
}

$msg = "";

// Handle POST actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  if (isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'add_template') {
      $name = trim($_POST['name'] ?? '');
      $start_time = $_POST['start_time'] ?? '';
      $end_time = $_POST['end_time'] ?? '';
      $desc = trim($_POST['description'] ?? '');

      if ($name && $start_time && $end_time) {
        $stmt = $mysqli->prepare("INSERT INTO shift_templates (name, start_time, end_time, description) VALUES (?, ?, ?, ?)");
        $stmt->bind_param('ssss', $name, $start_time, $end_time, $desc);
        if ($stmt->execute()) {
          $msg = "Shift template \"$name\" added.";
        } else {
          $msg = "Failed to add shift template (it may already exist).";
        }
        $stmt->close();
      } else {
        $msg = "Please fill all template fields.";
      }
    } elseif ($action === 'assign_shift') {
      $employee_id = intval($_POST['employee_id'] ?? 0);
      $shift_template_id = intval($_POST['shift_template_id'] ?? 0);
      $shift_date = $_POST['shift_date'] ?? '';

      if ($employee_id && $shift_template_id && $shift_date) {
        $stmt = $mysqli->prepare("INSERT INTO employee_shifts (employee_id, shift_template_id, shift_date) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE shift_template_id = VALUES(shift_template_id)");
        $stmt->bind_param('iis', $employee_id, $shift_template_id, $shift_date);
        if ($stmt->execute()) {
          $msg = "Assigned shift to employee.";
        } else {
          $msg = "Failed to assign shift.";
        }
        $stmt->close();
      } else {
        $msg = "Please select employee, shift template, and date to assign.";
      }
    } elseif ($action === 'delete_template') {
      $template_id = intval($_POST['template_id'] ?? 0);
      if ($template_id) {
        $stmt = $mysqli->prepare("DELETE FROM shift_templates WHERE id = ?");
        $stmt->bind_param('i', $template_id);
        if ($stmt->execute()) {
          $msg = "Shift template deleted.";
        } else {
          $msg = "Failed to delete shift template.";
        }
        $stmt->close();
      }
    }
  }
}

// Fetch shift templates
$templates_result = $mysqli->query("SELECT * FROM shift_templates ORDER BY name");
$shift_templates = $templates_result->fetch_all(MYSQLI_ASSOC);

// Fetch employees
$employees_result = $mysqli->query("SELECT id, name FROM employees ORDER BY name");
$employees = $employees_result->fetch_all(MYSQLI_ASSOC);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Manage Shifts - Attendance App</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
  <style>
    body {
      max-width: 900px;
      margin: 20px auto;
      background: #f8f9fa;
    }
    h1 {
      color: #0d6efd;
      font-weight: 700;
      margin-bottom: 30px;
      text-align: center;
    }
    .card {
      box-shadow: 0 0 6px rgba(0,0,0,.1);
      border-radius: 8px;
      padding: 20px;
      background: white;
      margin-bottom: 30px;
    }
  </style>
</head>
<body>
<div class="container">
  <h1>Manage Shifts</h1>

  <?php if ($msg): ?>
    <div class="alert alert-info"><?= htmlspecialchars($msg) ?></div>
  <?php endif; ?>

  <div class="card">
    <h4>Add Shift Template</h4>
    <form method="POST" class="mb-4">
      <input type="hidden" name="action" value="add_template" />
      <div class="mb-3">
        <label for="name" class="form-label">Template Name</label>
        <input type="text" class="form-control" id="name" name="name" placeholder="e.g. Day Shift" required />
      </div>
      <div class="mb-3 row">
        <div class="col">
          <label for="start_time" class="form-label">Start Time</label>
          <input type="time" class="form-control" id="start_time" name="start_time" required />
        </div>
        <div class="col">
          <label for="end_time" class="form-label">End Time</label>
          <input type="time" class="form-control" id="end_time" name="end_time" required />
        </div>
      </div>
      <div class="mb-3">
        <label for="description" class="form-label">Description (optional)</label>
        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
      </div>
      <button type="submit" class="btn btn-primary">Add Template</button>
    </form>

    <h5>Existing Shift Templates</h5>
    <?php if (empty($shift_templates)): ?>
      <p>No shift templates created yet.</p>
    <?php else: ?>
      <table class="table table-sm table-striped">
        <thead>
          <tr>
            <th>Name</th>
            <th>Start</th>
            <th>End</th>
            <th>Description</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
        <?php foreach ($shift_templates as $tpl): ?>
          <tr>
            <td><?= htmlspecialchars($tpl['name']) ?></td>
            <td><?= htmlspecialchars(substr($tpl['start_time'], 0, 5)) ?></td>
            <td><?= htmlspecialchars(substr($tpl['end_time'], 0, 5)) ?></td>
            <td><?= nl2br(htmlspecialchars($tpl['description'])) ?></td>
            <td>
              <form method="POST" style="display:inline;" onsubmit="return confirm('Delete shift template <?= htmlspecialchars($tpl['name']) ?>?');">
                <input type="hidden" name="action" value="delete_template" />
                <input type="hidden" name="template_id" value="<?= intval($tpl['id']) ?>" />
                <button type="submit" class="btn btn-sm btn-danger">Delete</button>
              </form>
            </td>
          </tr>
        <?php endforeach; ?>
        </tbody>
      </table>
    <?php endif; ?>
  </div>

  <div class="card">
    <h4>Assign Shift to Employee</h4>
    <form method="POST" class="row g-3">
      <input type="hidden" name="action" value="assign_shift" />

      <div class="col-md-4">
        <label for="employee_id" class="form-label">Employee</label>
        <select id="employee_id" name="employee_id" class="form-select" required>
          <option value="" selected>Select employee</option>
          <?php foreach ($employees as $emp): ?>
            <option value="<?= intval($emp['id']) ?>"><?= htmlspecialchars($emp['name']) ?></option>
          <?php endforeach; ?>
        </select>
      </div>

      <div class="col-md-4">
        <label for="shift_template_id" class="form-label">Shift Template</label>
        <select id="shift_template_id" name="shift_template_id" class="form-select" required>
          <option value="" selected>Select shift template</option>
          <?php foreach ($shift_templates as $tpl): ?>
            <option value="<?= intval($tpl['id']) ?>"><?= htmlspecialchars($tpl['name']) ?></option>
          <?php endforeach; ?>
        </select>
      </div>

      <div class="col-md-4">
        <!-- Single text input for date range -->
        <label for="date_range" class="form-label">Select Date Range</label>
        <input type="text" id="date_range" name="date_range" class="form-control" placeholder="Select date range" required />
      </div>

      <div class="col-12">
        <button type="submit" class="btn btn-success">Assign Shift</button>
      </div>
    </form>
  </div>

  <a href="dashboard.php" class="btn btn-secondary mt-3">Back to Dashboard</a>
</div>

<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
  flatpickr("#date_range", {
    mode: "range",
    dateFormat: "Y-m-d",
    minDate: "today"
  });
</script>

</body>
</html>
